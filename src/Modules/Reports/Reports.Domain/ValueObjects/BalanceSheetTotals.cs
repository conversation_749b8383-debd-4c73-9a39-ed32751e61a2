using Orion.SharedKernel.Domain.ValueObjects;

namespace Reports.Domain.ValueObjects;

public class BalanceSheetTotals : ValueObject
{
    public decimal UrbanCleaningTons { get; init; }
    public decimal SweepingTons { get; init; }
    public decimal NonRecyclableTons { get; init; }
    public decimal RejectionTons { get; init; }
    public decimal RecyclableTons { get; init; }
    public decimal TotalF14 { get; init; }
    public decimal TotalByNUAP { get; init; }
    public decimal Discounts { get; init; }
    public decimal TotalF34 { get; init; }
    public decimal Difference { get; init; }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return UrbanCleaningTons;
        yield return SweepingTons;
        yield return NonRecyclableTons;
        yield return RejectionTons;
        yield return RecyclableTons;
        yield return TotalF14;
        yield return TotalByNUAP;
        yield return Discounts;
        yield return TotalF34;
        yield return Difference;
    }
}
