using Orion.SharedKernel.Domain.ValueObjects;

namespace Reports.Domain.ValueObjects;

public class DistributionTotals : ValueObject
{
    public decimal ReportedTons { get; init; }
    public int Trips { get; init; }
    public decimal CalculatedDistributedTons { get; init; }
    public decimal DistributionTollPercentage { get; init; }
    public decimal DeviationTons { get; init; }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ReportedTons;
        yield return Trips;
        yield return CalculatedDistributedTons;
        yield return DistributionTollPercentage;
        yield return DeviationTons;
    }
}
