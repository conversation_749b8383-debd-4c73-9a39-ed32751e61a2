namespace Reports.Application.Common.Constants;

public enum TableType
{
    BalanceSheet,
    Summary,
    Distribution
}

public enum ColumnIndex
{
    First = 0,
    Second = 1,
}

public enum BalanceSheetColumn
{
    AreaCode = 0,
    AreaName = 1,
    UrbanCleaning = 2,
    Sweeping = 3,
    NonRecyclable = 4,
    Rejection = 5,
    Recyclable = 6,
    TotalF14 = 7,
    TotalByNuap = 8,
    Discounts = 9,
    TotalF34 = 10,
    Difference = 11
}

public enum DistributionColumn
{
    RecyclingArea = 0,
    ReportedTons = 1,
    Trips = 2,
    CalculatedDistributedTons = 3,
    TollSharedRouteTons = 4,
    DistributionTollPercentage = 5,
    DeviationTons = 6
}

public enum SummaryColumn
{
    TotalDispositionFinal = 0,
    TotalEmvarias = 1
}

public record ExcelFormatting(
    string DecimalFormat = "#,##0.000",
    string IntegerFormat = "#,##0",
    string CompensationFormat = "#,##0.000;-#,##0.000;0.000",
    short TitleFontSize = 16
);

public record TableConfiguration(
    int ColumnCount,
    int DataStartColumnIndex,
    string Title,
    string TotalLabel,
    HashSet<int> IntegerColumns,
    HashSet<int> CompensationColumns
);

public record LayoutConstants(
    int AutoSizeColumnCount = 15,
    int TitleRowSpan = 3,
    int HeaderSpacingRows = 1,
    int SectionSpacingRows = 2,
    int DataRowStartOffset = 1,
    int DataRowEndOffset = 1,
    int HeaderRowOffset = 1,
    int MergeRegionRowOffset = 1,
    int NotFoundIndex = -1
);

public record ColumnWidths(
    int TimestampColumn = 25 * 256,
    int TotalByNuapColumn = 18 * 256
);

public record DateFormats(
    string DefaultExcelFormat = "yyyy/MM/dd HH:mm:ss",
    string HeaderFormat = "dd/MM/yyyy HH:mm"
);

public record Labels(
    string GeneratedPrefix = "Generado: ",
    string PeriodPrefix = "Período: ",
    string FilePrefix = "Balance_de_Masas_",
    string OtrosAreaName = "Otros",
    string EmptyString = "",
    string DashValue = "-",
    double ZeroValue = 0.0
);

public record Keywords(
    string Total = "TOTAL",
    string Sum = "SUMA",
    string Concept = "Concepto",
    string RecyclingArea = "Área de Reciclaje"
);

public static class MassBalanceExcelConstants
{
    public static readonly ExcelFormatting Formatting = new();
    public static readonly LayoutConstants Layout = new();
    public static readonly ColumnWidths Widths = new();
    public static readonly DateFormats Dates = new();
    public static readonly Labels Text = new();
    public static readonly Keywords Search = new();

    public static readonly Dictionary<TableType, TableConfiguration> Tables = new()
    {
        {
            TableType.BalanceSheet,
            new TableConfiguration(
                ColumnCount: 12,
                DataStartColumnIndex: 2,
                Title: "Balance de Masas",
                TotalLabel: "SUMA TOTAL",
                IntegerColumns: new HashSet<int>(),
                CompensationColumns: new HashSet<int>()
            )
        },
        {
            TableType.Summary,
            new TableConfiguration(
                ColumnCount: 2,
                DataStartColumnIndex: 0,
                Title: "Disposición Final",
                TotalLabel: "TOTAL",
                IntegerColumns: new HashSet<int>(),
                CompensationColumns: new HashSet<int>()
            )
        },
        {
            TableType.Distribution,
            new TableConfiguration(
                ColumnCount: 7,
                DataStartColumnIndex: 1,
                Title: "Distribución",
                TotalLabel: "TOTAL",
                IntegerColumns: new HashSet<int> { 2 },
                CompensationColumns: new HashSet<int> { 6 }
            )
        }
    };

    public static readonly Dictionary<BalanceSheetColumn, string> BalanceSheetHeaders = new()
    {
        { BalanceSheetColumn.AreaCode, "Área de Prestación" },
        { BalanceSheetColumn.AreaName, "Nombre de Área de Prestación" },
        { BalanceSheetColumn.UrbanCleaning, "Toneladas de Limpieza Urbana" },
        { BalanceSheetColumn.Sweeping, "Toneladas de Barrido" },
        { BalanceSheetColumn.NonRecyclable, "Toneladas de Residuos No Aprovechables" },
        { BalanceSheetColumn.Rejection, "Toneladas de Rechazos de Residuos Aprovechados" },
        { BalanceSheetColumn.Recyclable, "Toneladas de Residuos Aprovechables" },
        { BalanceSheetColumn.TotalF14, "Toneladas de Barrido + Toneladas de Residuos No Aprovechables" },
        { BalanceSheetColumn.TotalByNuap, "Total por NUAP" },
        { BalanceSheetColumn.Discounts, "Descuentos" },
        { BalanceSheetColumn.TotalF34, "Total por NUAP - Descuentos" },
        { BalanceSheetColumn.Difference, "Diferencia (F34-F14)" }
    };

    public static readonly Dictionary<DistributionColumn, string> DistributionHeaders = new()
    {
        { DistributionColumn.RecyclingArea, "Área de Reciclaje" },
        { DistributionColumn.ReportedTons, "Toneladas Facturadas" },
        { DistributionColumn.Trips, "Nro Viajes" },
        { DistributionColumn.CalculatedDistributedTons, "Tonxviaje" },
        { DistributionColumn.TollSharedRouteTons, "Toneladas Totales Rutas Compartidas" },
        { DistributionColumn.DistributionTollPercentage, "% Distpeaje" },
        { DistributionColumn.DeviationTons, "Toneladas a Compensar" }
    };

    public static readonly Dictionary<SummaryColumn, string> SummaryHeaders = new()
    {
        { SummaryColumn.TotalDispositionFinal, "Total Disposición Final" },
        { SummaryColumn.TotalEmvarias, "Total Emvarias" }
    };

    public static readonly Dictionary<BalanceSheetColumn, string> BalanceSheetSubHeaders = new()
    {
        { BalanceSheetColumn.AreaCode, "" },
        { BalanceSheetColumn.AreaName, "" },
        { BalanceSheetColumn.UrbanCleaning, "F14 Toneladas Provenientes del Área de Prestación" },
        { BalanceSheetColumn.Sweeping, "" },
        { BalanceSheetColumn.NonRecyclable, "" },
        { BalanceSheetColumn.Rejection, "" },
        { BalanceSheetColumn.Recyclable, "" },
        { BalanceSheetColumn.TotalF14, "" },
        { BalanceSheetColumn.TotalByNuap, "F34 Disposición Final Operador" },
        { BalanceSheetColumn.Discounts, "" },
        { BalanceSheetColumn.TotalF34, "" },
        { BalanceSheetColumn.Difference, "" }
    };

    public const string MainTitle = "Balance de Masas";

    public static TableConfiguration GetTableConfig(TableType tableType) => Tables[tableType];
    public static string GetHeader(BalanceSheetColumn column) => BalanceSheetHeaders[column];
    public static string GetHeader(DistributionColumn column) => DistributionHeaders[column];
    public static string GetHeader(SummaryColumn column) => SummaryHeaders[column];
    public static string GetSubHeader(BalanceSheetColumn column) => BalanceSheetSubHeaders[column];
}
